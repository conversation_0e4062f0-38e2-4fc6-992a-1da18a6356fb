name: Update Docker Hub Description
on:
  push:
    branches:
    - stable

# disable permissions for all of the available permissions
permissions: {}

jobs:
  dockerHubDescription:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Docker Hub Description
      uses: peter-evans/dockerhub-description@v4
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: freqtradeorg/freqtrade
