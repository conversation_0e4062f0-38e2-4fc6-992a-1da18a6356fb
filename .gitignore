# Freqtrade rules
config*.json
*.sqlite
*.sqlite-shm
*.sqlite-wal
logfile.txt
user_data/*
!user_data/strategy/sample_strategy.py
!user_data/notebooks
!user_data/models
!user_data/freqaimodels
user_data/freqaimodels/*
user_data/models/*
user_data/notebooks/*
freqtrade-plot.html
freqtrade-profit-plot.html
freqtrade/rpc/api_server/ui/*
build_helpers/ta-lib/*

# Macos related
.DS_Store

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# memray
memray-*

# Sphinx documentation
docs/_build/
# Mkdocs documentation
site/

# PyBuilder
target/

# Jupyter Notebook
*.ipynb_checkpoints

# pyenv
.python-version

.env
.venv
.idea
.vscode

.pytest_cache/
.mypy_cache/

#exceptions
!*.gitkeep
!config_examples/config_binance.example.json
!config_examples/config_full.example.json
!config_examples/config_kraken.example.json
!config_examples/config_freqai.example.json

docker-compose-*.yml
