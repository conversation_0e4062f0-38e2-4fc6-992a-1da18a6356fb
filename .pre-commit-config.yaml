# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
repos:
  - repo: https://github.com/pycqa/flake8
    rev: "7.1.2"
    hooks:
      - id: flake8
        additional_dependencies: [Flake8-pyproject]
        # stages: [push]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: "v1.15.0"
    hooks:
      - id: mypy
        exclude: build_helpers
        additional_dependencies:
          - types-cachetools==5.5.0.20240820
          - types-filelock==3.2.7
          - types-requests==2.32.0.20250306
          - types-tabulate==0.9.0.20241207
          - types-python-dateutil==2.9.0.20241206
          - SQLAlchemy==2.0.39
        # stages: [push]

  - repo: https://github.com/pycqa/isort
    rev: "6.0.1"
    hooks:
      - id: isort
        name: isort (python)
        # stages: [push]

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    # Ruff version.
    rev: 'v0.11.0'
    hooks:
      - id: ruff
      - id: ruff-format

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: end-of-file-fixer
        exclude: |
            (?x)^(
                tests/.*|
                .*\.svg|
                .*\.yml|
                .*\.json
            )$
      - id: mixed-line-ending
      - id: debug-statements
      - id: check-ast
      - id: trailing-whitespace
        exclude: |
          (?x)^(
                .*\.md
            )$

  - repo: https://github.com/stefmolin/exif-stripper
    rev: 0.6.2
    hooks:
      - id: strip-exif

  - repo: https://github.com/codespell-project/codespell
    rev: v2.4.1
    hooks:
    - id: codespell
      additional_dependencies:
        - tomli

  # Ensure github actions remain safe
  - repo: https://github.com/woodruffw/zizmor-pre-commit
    rev: v1.5.1
    hooks:
    - id: zizmor
