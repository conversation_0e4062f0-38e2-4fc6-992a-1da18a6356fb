# --- Do not remove these libs ---
from freqtrade.strategy.interface import IStrategy
from pandas import DataFrame
import talib.abstract as ta
import numpy as np
import pandas as pd
from freqtrade.strategy import DecimalParameter, IntParameter, BooleanParameter
from functools import reduce
import logging
from datetime import datetime, timedelta
from freqtrade.persistence import Trade

logger = logging.getLogger(__name__)

class ReversalCandlestickStrategy(IStrategy):
    """
    Reversal Candlestick Strategy based on LuxAlgo's Reversal Candlestick Structure indicator
    This strategy identifies various candlestick patterns for potential trend reversals
    
    Features:
    - Trend detection using stochastic oscillator with smoothing
    - Overbought/Oversold filter to improve entry and exit points
    - Candlestick pattern recognition using TA-Lib functions
    
    Can be used for both long and short positions:
    - Long entries: Bullish patterns (Hammer, Inverted Hammer, Bullish Engulfing, etc.) in oversold conditions
    - Long exits: Bearish patterns (Hanging Man, Shooting Star, Bearish Engulfing, etc.) in overbought conditions
    - Short entries: Bearish patterns in overbought conditions
    - Short exits: Bullish patterns in oversold conditions
    """
    
    # Strategy interface version
    INTERFACE_VERSION = 3

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.1
    }

    # Optimal stoploss designed for the strategy
    stoploss = -0.05

    # Trailing stoploss
    trailing_stop = False
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True

    # Timeframe for the strategy
    timeframe = '1h'

    # Run "populate_indicators" only for new candle.
    process_only_new_candles = True

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 30

    # Optional order type mapping
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional time in force for orders
    order_time_in_force = {
        'entry': 'gtc',
        'exit': 'gtc'
    }
    
    # Enable/disable shorts
    can_short = True

    # Parameters for the strategy
    # Trend detection parameters
    trend_length = IntParameter(10, 20, default=14, space="buy", optimize=True)
    threshold = IntParameter(70, 90, default=80, space="buy", optimize=True)
    smooth = IntParameter(10, 30, default=20, space="buy", optimize=True)
    
    # Overbought/Oversold filter
    use_ob_os_filter = BooleanParameter(default=True, space="buy", optimize=False)
    
    # Pattern toggles (can be optimized if needed)
    use_hammer = BooleanParameter(default=True, space="buy", optimize=False)
    use_inv_hammer = BooleanParameter(default=True, space="buy", optimize=False)
    use_bull_engulfing = BooleanParameter(default=True, space="buy", optimize=False)
    use_rising_3 = BooleanParameter(default=True, space="buy", optimize=False)
    use_three_white_soldiers = BooleanParameter(default=True, space="buy", optimize=False)
    use_morning_star = BooleanParameter(default=True, space="buy", optimize=False)
    use_bull_harami = BooleanParameter(default=True, space="buy", optimize=False)
    use_tweezer_bottom = BooleanParameter(default=True, space="buy", optimize=False)
    use_piercing = BooleanParameter(default=True, space="buy", optimize=False)
    
    use_hanging_man = BooleanParameter(default=True, space="sell", optimize=False)
    use_shooting_star = BooleanParameter(default=True, space="sell", optimize=False)
    use_bear_engulfing = BooleanParameter(default=True, space="sell", optimize=False)
    use_falling_3 = BooleanParameter(default=True, space="sell", optimize=False)
    use_three_black_crows = BooleanParameter(default=True, space="sell", optimize=False)
    use_evening_star = BooleanParameter(default=True, space="sell", optimize=False)
    use_bear_harami = BooleanParameter(default=True, space="sell", optimize=False)
    use_tweezer_top = BooleanParameter(default=True, space="sell", optimize=False)
    use_dark_cloud = BooleanParameter(default=True, space="sell", optimize=False)

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Compute all indicators required for the strategy
        """
        # Initialize columns
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        
        # Trend Detection
        length = self.trend_length.value
        threshold = self.threshold.value
        smooth_val = self.smooth.value
        
        # Calculate stochastic
        stoch = ta.STOCH(dataframe, fastk_period=length)
        dataframe['k'] = stoch['slowk']
        
        # Calculate smoothed stochastic using EMA-like formula
        # This is a more efficient implementation of the TradingView script's smoothing logic
        dataframe['smooth_k'] = 0.0
        alpha = 2 / (smooth_val + 1)
        
        # Initialize smooth_k
        if 'smooth_k' not in dataframe.columns:
            dataframe['smooth_k'] = 0.0

        # Calculate alpha for smoothing
        alpha = 2 / (smooth_val + 1)

        # Apply smoothing logic with proper loc indexing instead of chained indexing
        for i in range(1, len(dataframe)):
            if dataframe.iloc[i]['k'] > 50:
                dataframe.loc[i, 'smooth_k'] = dataframe.iloc[i-1]['smooth_k'] + (100 - dataframe.iloc[i-1]['smooth_k']) * alpha
            elif dataframe.iloc[i]['k'] < 50:
                dataframe.loc[i, 'smooth_k'] = dataframe.iloc[i-1]['smooth_k'] + (0 - dataframe.iloc[i-1]['smooth_k']) * alpha
            else:
                dataframe.loc[i, 'smooth_k'] = dataframe.iloc[i]['k']
        
        # Determine trend conditions - overbought/oversold zones
        # Overbought: k and smooth_k are above threshold
        # Oversold: k and smooth_k are below (100-threshold)
        dataframe['is_overbought'] = (dataframe['k'] >= threshold) & (dataframe['smooth_k'] >= threshold)
        dataframe['is_oversold'] = (dataframe['k'] <= (100 - threshold)) & (dataframe['smooth_k'] <= (100 - threshold))
        
        # Identify red and green candles
        dataframe['red_candle'] = dataframe['close'] < dataframe['open']
        dataframe['green_candle'] = dataframe['close'] > dataframe['open']
        
        # Use TA-Lib for candlestick pattern recognition
        
        # Bullish patterns
        dataframe['CDLHAMMER'] = ta.CDLHAMMER(dataframe)
        dataframe['CDLINVERTEDHAMMER'] = ta.CDLINVERTEDHAMMER(dataframe)
        dataframe['CDLENGULFING'] = ta.CDLENGULFING(dataframe)
        dataframe['CDLMORNINGSTAR'] = ta.CDLMORNINGSTAR(dataframe)
        dataframe['CDLHARAMI'] = ta.CDLHARAMI(dataframe)
        dataframe['CDLPIERCING'] = ta.CDLPIERCING(dataframe)
        dataframe['CDL3WHITESOLDIERS'] = ta.CDL3WHITESOLDIERS(dataframe)
        
        # Bearish patterns
        dataframe['CDLHANGINGMAN'] = ta.CDLHANGINGMAN(dataframe)
        dataframe['CDLSHOOTINGSTAR'] = ta.CDLSHOOTINGSTAR(dataframe)
        dataframe['CDLEVENINGSTAR'] = ta.CDLEVENINGSTAR(dataframe)
        dataframe['CDL3BLACKCROWS'] = ta.CDL3BLACKCROWS(dataframe)
        dataframe['CDLDARKCLOUDCOVER'] = ta.CDLDARKCLOUDCOVER(dataframe)
        
        # Additional patterns
        dataframe['CDLDOJI'] = ta.CDLDOJI(dataframe)
        
        # Tweezer patterns (not directly available in TA-Lib)
        # Tweezer Bottom: Two candles with equal lows, first bearish, second bullish
        dataframe['tweezer_btm'] = (
            (dataframe['low'].round(6) == dataframe['low'].shift(1).round(6)) & 
            (dataframe['green_candle']) & 
            (dataframe['red_candle'].shift(1))
        )
        
        # Tweezer Top: Two candles with equal highs, first bullish, second bearish
        dataframe['tweezer_top'] = (
            (dataframe['high'].round(6) == dataframe['high'].shift(1).round(6)) & 
            (dataframe['red_candle']) & 
            (dataframe['green_candle'].shift(1))
        )
        
        # Rising Three Method (not directly available in TA-Lib)
        dataframe['rising_3'] = (
            (dataframe['green_candle'].shift(4)) & 
            (dataframe['red_candle'].shift(3)) & 
            (dataframe['red_candle'].shift(2)) & 
            (dataframe['red_candle'].shift(1)) & 
            (dataframe['green_candle']) & 
            (dataframe['close'] > dataframe['high'].shift(4))
        )
        
        # Falling Three Method (not directly available in TA-Lib)
        dataframe['falling_3'] = (
            (dataframe['red_candle'].shift(4)) & 
            (dataframe['green_candle'].shift(3)) & 
            (dataframe['green_candle'].shift(2)) & 
            (dataframe['green_candle'].shift(1)) & 
            (dataframe['red_candle']) & 
            (dataframe['close'] < dataframe['low'].shift(4))
        )
        
        # Convert TA-Lib signals to boolean and filter with trend conditions
        
        # Bullish patterns with oversold filter (if enabled)
        if self.use_ob_os_filter.value:
            # Bullish patterns should occur in oversold conditions
            dataframe['hammer'] = (dataframe['CDLHAMMER'] > 0) & dataframe['is_oversold']
            dataframe['inv_hammer'] = (dataframe['CDLINVERTEDHAMMER'] > 0) & dataframe['is_oversold']
            dataframe['bull_engulfing'] = (dataframe['CDLENGULFING'] > 0) & dataframe['is_oversold'].shift(1)
            dataframe['m_star'] = (dataframe['CDLMORNINGSTAR'] > 0) & dataframe['is_oversold']
            dataframe['bull_harami'] = (dataframe['CDLHARAMI'] > 0) & dataframe['is_oversold']
            dataframe['piercing'] = (dataframe['CDLPIERCING'] > 0) & dataframe['is_oversold'].shift(1)
            dataframe['soldiers'] = (dataframe['CDL3WHITESOLDIERS'] > 0) & dataframe['is_oversold'].shift(3)
            dataframe['tweezer_btm'] = dataframe['tweezer_btm'] & dataframe['is_oversold'].shift(1)
            dataframe['rising_3'] = dataframe['rising_3'] & dataframe['is_oversold'].shift(4)
            
            # Bearish patterns should occur in overbought conditions
            dataframe['h_man'] = (dataframe['CDLHANGINGMAN'] > 0) & dataframe['is_overbought']
            dataframe['s_star'] = (dataframe['CDLSHOOTINGSTAR'] > 0) & dataframe['is_overbought']
            dataframe['e_star'] = (dataframe['CDLEVENINGSTAR'] > 0) & dataframe['is_overbought']
            dataframe['bear_engulfing'] = (dataframe['CDLENGULFING'] < 0) & dataframe['is_overbought'].shift(1)
            dataframe['bear_harami'] = (dataframe['CDLHARAMI'] < 0) & dataframe['is_overbought']
            dataframe['dark_cloud'] = (dataframe['CDLDARKCLOUDCOVER'] > 0) & dataframe['is_overbought'].shift(1)
            dataframe['crows'] = (dataframe['CDL3BLACKCROWS'] > 0) & dataframe['is_overbought'].shift(3)
            dataframe['tweezer_top'] = dataframe['tweezer_top'] & dataframe['is_overbought'].shift(1)
            dataframe['falling_3'] = dataframe['falling_3'] & dataframe['is_overbought'].shift(4)
        else:
            # Without overbought/oversold filter, just use the pattern signals
            dataframe['hammer'] = (dataframe['CDLHAMMER'] > 0)
            dataframe['inv_hammer'] = (dataframe['CDLINVERTEDHAMMER'] > 0)
            dataframe['bull_engulfing'] = (dataframe['CDLENGULFING'] > 0)
            dataframe['m_star'] = (dataframe['CDLMORNINGSTAR'] > 0)
            dataframe['bull_harami'] = (dataframe['CDLHARAMI'] > 0)
            dataframe['piercing'] = (dataframe['CDLPIERCING'] > 0)
            dataframe['soldiers'] = (dataframe['CDL3WHITESOLDIERS'] > 0)
            dataframe['tweezer_btm'] = dataframe['tweezer_btm']
            dataframe['rising_3'] = dataframe['rising_3']
            
            dataframe['h_man'] = (dataframe['CDLHANGINGMAN'] > 0)
            dataframe['s_star'] = (dataframe['CDLSHOOTINGSTAR'] > 0)
            dataframe['e_star'] = (dataframe['CDLEVENINGSTAR'] > 0)
            dataframe['bear_engulfing'] = (dataframe['CDLENGULFING'] < 0)
            dataframe['bear_harami'] = (dataframe['CDLHARAMI'] < 0)
            dataframe['dark_cloud'] = (dataframe['CDLDARKCLOUDCOVER'] > 0)
            dataframe['crows'] = (dataframe['CDL3BLACKCROWS'] > 0)
            dataframe['tweezer_top'] = dataframe['tweezer_top']
            dataframe['falling_3'] = dataframe['falling_3']
        
        return dataframe
        
        # Use TA-Lib for candlestick pattern recognition
        
        # Bullish patterns
        dataframe['CDLHAMMER'] = ta.CDLHAMMER(dataframe)
        dataframe['CDLINVERTEDHAMMER'] = ta.CDLINVERTEDHAMMER(dataframe)
        dataframe['CDLENGULFING'] = ta.CDLENGULFING(dataframe)
        dataframe['CDLMORNINGSTAR'] = ta.CDLMORNINGSTAR(dataframe)
        dataframe['CDLHARAMI'] = ta.CDLHARAMI(dataframe)
        dataframe['CDLPIERCING'] = ta.CDLPIERCING(dataframe)
        dataframe['CDL3WHITESOLDIERS'] = ta.CDL3WHITESOLDIERS(dataframe)
        
        # Bearish patterns
        dataframe['CDLHANGINGMAN'] = ta.CDLHANGINGMAN(dataframe)
        dataframe['CDLSHOOTINGSTAR'] = ta.CDLSHOOTINGSTAR(dataframe)
        dataframe['CDLEVENINGSTAR'] = ta.CDLEVENINGSTAR(dataframe)
        dataframe['CDL3BLACKCROWS'] = ta.CDL3BLACKCROWS(dataframe)
        dataframe['CDLDARKCLOUDCOVER'] = ta.CDLDARKCLOUDCOVER(dataframe)
        
        # Additional patterns
        dataframe['CDLDOJI'] = ta.CDLDOJI(dataframe)
        
        # Tweezer patterns (not directly available in TA-Lib)
        # Tweezer Bottom: Two candles with equal lows, first bearish, second bullish
        dataframe['tweezer_btm'] = (
            (dataframe['low'].round(6) == dataframe['low'].shift(1).round(6)) & 
            (dataframe['green_candle']) & 
            (dataframe['red_candle'].shift(1))
        )
        
        # Tweezer Top: Two candles with equal highs, first bullish, second bearish
        dataframe['tweezer_top'] = (
            (dataframe['high'].round(6) == dataframe['high'].shift(1).round(6)) & 
            (dataframe['red_candle']) & 
            (dataframe['green_candle'].shift(1))
        )
        
        # Rising Three Method (not directly available in TA-Lib)
        dataframe['rising_3'] = (
            (dataframe['green_candle'].shift(4)) & 
            (dataframe['red_candle'].shift(3)) & 
            (dataframe['red_candle'].shift(2)) & 
            (dataframe['red_candle'].shift(1)) & 
            (dataframe['green_candle']) & 
            (dataframe['close'] > dataframe['high'].shift(4))
        )
        
        # Falling Three Method (not directly available in TA-Lib)
        dataframe['falling_3'] = (
            (dataframe['red_candle'].shift(4)) & 
            (dataframe['green_candle'].shift(3)) & 
            (dataframe['green_candle'].shift(2)) & 
            (dataframe['green_candle'].shift(1)) & 
            (dataframe['red_candle']) & 
            (dataframe['close'] < dataframe['low'].shift(4))
        )
        
        # Convert TA-Lib signals to boolean and filter with trend conditions
        
        # Bullish patterns with bearish trend filter
        dataframe['hammer'] = (dataframe['CDLHAMMER'] > 0) & dataframe['is_bearish']
        dataframe['inv_hammer'] = (dataframe['CDLINVERTEDHAMMER'] > 0) & dataframe['is_bearish']
        dataframe['bull_engulfing'] = (dataframe['CDLENGULFING'] > 0) & dataframe['is_bearish'].shift(1)
        dataframe['m_star'] = (dataframe['CDLMORNINGSTAR'] > 0) & dataframe['is_bearish']
        dataframe['bull_harami'] = (dataframe['CDLHARAMI'] > 0) & dataframe['is_bearish']
        dataframe['piercing'] = (dataframe['CDLPIERCING'] > 0) & dataframe['is_bearish'].shift(1)
        dataframe['soldiers'] = (dataframe['CDL3WHITESOLDIERS'] > 0) & dataframe['is_bearish'].shift(3)
        dataframe['tweezer_btm'] = dataframe['tweezer_btm'] & dataframe['is_bearish'].shift(1)
        dataframe['rising_3'] = dataframe['rising_3'] & dataframe['is_bearish'].shift(4)
        
        # Bearish patterns with bullish trend filter
        dataframe['h_man'] = (dataframe['CDLHANGINGMAN'] > 0) & dataframe['is_bullish']
        dataframe['s_star'] = (dataframe['CDLSHOOTINGSTAR'] > 0) & dataframe['is_bullish']
        dataframe['e_star'] = (dataframe['CDLEVENINGSTAR'] > 0) & dataframe['is_bullish']
        dataframe['bear_engulfing'] = (dataframe['CDLENGULFING'] < 0) & dataframe['is_bullish'].shift(1)
        dataframe['bear_harami'] = (dataframe['CDLHARAMI'] < 0) & dataframe['is_bullish']
        dataframe['dark_cloud'] = (dataframe['CDLDARKCLOUDCOVER'] > 0) & dataframe['is_bullish'].shift(1)
        dataframe['crows'] = (dataframe['CDL3BLACKCROWS'] > 0) & dataframe['is_bullish'].shift(3)
        dataframe['tweezer_top'] = dataframe['tweezer_top'] & dataframe['is_bullish'].shift(1)
        dataframe['falling_3'] = dataframe['falling_3'] & dataframe['is_bullish'].shift(4)
        
        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, define the entry signal for the pair
        """
        dataframe['enter_tag'] = ''
        
        # LONG ENTRIES - Bullish patterns for buy signals with tags
        # These should occur in oversold conditions for better entries
        if self.use_hammer.value:
            dataframe.loc[dataframe['hammer'], 'enter_tag'] = 'hammer_long'
            dataframe.loc[dataframe['hammer'], 'enter_long'] = 1
            
        if self.use_inv_hammer.value:
            dataframe.loc[dataframe['inv_hammer'], 'enter_tag'] = 'inv_hammer_long'
            dataframe.loc[dataframe['inv_hammer'], 'enter_long'] = 1
            
        if self.use_bull_engulfing.value:
            dataframe.loc[dataframe['bull_engulfing'], 'enter_tag'] = 'bull_engulfing_long'
            dataframe.loc[dataframe['bull_engulfing'], 'enter_long'] = 1
            
        if self.use_rising_3.value:
            dataframe.loc[dataframe['rising_3'], 'enter_tag'] = 'rising_3_long'
            dataframe.loc[dataframe['rising_3'], 'enter_long'] = 1
            
        if self.use_three_white_soldiers.value:
            dataframe.loc[dataframe['soldiers'], 'enter_tag'] = 'three_white_soldiers_long'
            dataframe.loc[dataframe['soldiers'], 'enter_long'] = 1
            
        if self.use_morning_star.value:
            dataframe.loc[dataframe['m_star'], 'enter_tag'] = 'morning_star_long'
            dataframe.loc[dataframe['m_star'], 'enter_long'] = 1
            
        if self.use_bull_harami.value:
            dataframe.loc[dataframe['bull_harami'], 'enter_tag'] = 'bull_harami_long'
            dataframe.loc[dataframe['bull_harami'], 'enter_long'] = 1
            
        if self.use_tweezer_bottom.value:
            dataframe.loc[dataframe['tweezer_btm'], 'enter_tag'] = 'tweezer_bottom_long'
            dataframe.loc[dataframe['tweezer_btm'], 'enter_long'] = 1
            
        if self.use_piercing.value:
            dataframe.loc[dataframe['piercing'], 'enter_tag'] = 'piercing_long'
            dataframe.loc[dataframe['piercing'], 'enter_long'] = 1
        
        # SHORT ENTRIES - Bearish patterns for short signals with tags
        # These should occur in overbought conditions for better entries
        if self.can_short:
            if self.use_hanging_man.value:
                dataframe.loc[dataframe['h_man'], 'enter_tag'] = 'hanging_man_short'
                dataframe.loc[dataframe['h_man'], 'enter_short'] = 1
                
            if self.use_shooting_star.value:
                dataframe.loc[dataframe['s_star'], 'enter_tag'] = 'shooting_star_short'
                dataframe.loc[dataframe['s_star'], 'enter_short'] = 1
                
            if self.use_bear_engulfing.value:
                dataframe.loc[dataframe['bear_engulfing'], 'enter_tag'] = 'bear_engulfing_short'
                dataframe.loc[dataframe['bear_engulfing'], 'enter_short'] = 1
                
            if self.use_falling_3.value:
                dataframe.loc[dataframe['falling_3'], 'enter_tag'] = 'falling_3_short'
                dataframe.loc[dataframe['falling_3'], 'enter_short'] = 1
                
            if self.use_three_black_crows.value:
                dataframe.loc[dataframe['crows'], 'enter_tag'] = 'three_black_crows_short'
                dataframe.loc[dataframe['crows'], 'enter_short'] = 1
                
            if self.use_evening_star.value:
                dataframe.loc[dataframe['e_star'], 'enter_tag'] = 'evening_star_short'
                dataframe.loc[dataframe['e_star'], 'enter_short'] = 1
                
            if self.use_bear_harami.value:
                dataframe.loc[dataframe['bear_harami'], 'enter_tag'] = 'bear_harami_short'
                dataframe.loc[dataframe['bear_harami'], 'enter_short'] = 1
                
            if self.use_tweezer_top.value:
                dataframe.loc[dataframe['tweezer_top'], 'enter_tag'] = 'tweezer_top_short'
                dataframe.loc[dataframe['tweezer_top'], 'enter_short'] = 1
                
            if self.use_dark_cloud.value:
                dataframe.loc[dataframe['dark_cloud'], 'enter_tag'] = 'dark_cloud_short'
                dataframe.loc[dataframe['dark_cloud'], 'enter_short'] = 1
        
        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, define the exit signal for the pair
        """
        dataframe['exit_tag'] = ''
        
        # LONG EXITS - Bearish patterns for long exit signals with tags
        if self.use_hanging_man.value:
            dataframe.loc[dataframe['h_man'], 'exit_tag'] = 'hanging_man_long_exit'
            dataframe.loc[dataframe['h_man'], 'exit_long'] = 1
            
        if self.use_shooting_star.value:
            dataframe.loc[dataframe['s_star'], 'exit_tag'] = 'shooting_star_long_exit'
            dataframe.loc[dataframe['s_star'], 'exit_long'] = 1
            
        if self.use_bear_engulfing.value:
            dataframe.loc[dataframe['bear_engulfing'], 'exit_tag'] = 'bear_engulfing_long_exit'
            dataframe.loc[dataframe['bear_engulfing'], 'exit_long'] = 1
            
        if self.use_falling_3.value:
            dataframe.loc[dataframe['falling_3'], 'exit_tag'] = 'falling_3_long_exit'
            dataframe.loc[dataframe['falling_3'], 'exit_long'] = 1
            
        if self.use_three_black_crows.value:
            dataframe.loc[dataframe['crows'], 'exit_tag'] = 'three_black_crows_long_exit'
            dataframe.loc[dataframe['crows'], 'exit_long'] = 1
            
        if self.use_evening_star.value:
            dataframe.loc[dataframe['e_star'], 'exit_tag'] = 'evening_star_long_exit'
            dataframe.loc[dataframe['e_star'], 'exit_long'] = 1
            
        if self.use_bear_harami.value:
            dataframe.loc[dataframe['bear_harami'], 'exit_tag'] = 'bear_harami_long_exit'
            dataframe.loc[dataframe['bear_harami'], 'exit_long'] = 1
            
        if self.use_tweezer_top.value:
            dataframe.loc[dataframe['tweezer_top'], 'exit_tag'] = 'tweezer_top_long_exit'
            dataframe.loc[dataframe['tweezer_top'], 'exit_long'] = 1
            
        if self.use_dark_cloud.value:
            dataframe.loc[dataframe['dark_cloud'], 'exit_tag'] = 'dark_cloud_long_exit'
            dataframe.loc[dataframe['dark_cloud'], 'exit_long'] = 1
        
        # SHORT EXITS - Bullish patterns for short exit signals with tags
        if self.can_short:
            if self.use_hammer.value:
                dataframe.loc[dataframe['hammer'], 'exit_tag'] = 'hammer_short_exit'
                dataframe.loc[dataframe['hammer'], 'exit_short'] = 1
                
            if self.use_inv_hammer.value:
                dataframe.loc[dataframe['inv_hammer'], 'exit_tag'] = 'inv_hammer_short_exit'
                dataframe.loc[dataframe['inv_hammer'], 'exit_short'] = 1
                
            if self.use_bull_engulfing.value:
                dataframe.loc[dataframe['bull_engulfing'], 'exit_tag'] = 'bull_engulfing_short_exit'
                dataframe.loc[dataframe['bull_engulfing'], 'exit_short'] = 1
                
            if self.use_rising_3.value:
                dataframe.loc[dataframe['rising_3'], 'exit_tag'] = 'rising_3_short_exit'
                dataframe.loc[dataframe['rising_3'], 'exit_short'] = 1
                
            if self.use_three_white_soldiers.value:
                dataframe.loc[dataframe['soldiers'], 'exit_tag'] = 'three_white_soldiers_short_exit'
                dataframe.loc[dataframe['soldiers'], 'exit_short'] = 1
                
            if self.use_morning_star.value:
                dataframe.loc[dataframe['m_star'], 'exit_tag'] = 'morning_star_short_exit'
                dataframe.loc[dataframe['m_star'], 'exit_short'] = 1
                
            if self.use_bull_harami.value:
                dataframe.loc[dataframe['bull_harami'], 'exit_tag'] = 'bull_harami_short_exit'
                dataframe.loc[dataframe['bull_harami'], 'exit_short'] = 1
                
            if self.use_tweezer_bottom.value:
                dataframe.loc[dataframe['tweezer_btm'], 'exit_tag'] = 'tweezer_bottom_short_exit'
                dataframe.loc[dataframe['tweezer_btm'], 'exit_short'] = 1
                
            if self.use_piercing.value:
                dataframe.loc[dataframe['piercing'], 'exit_tag'] = 'piercing_short_exit'
                dataframe.loc[dataframe['piercing'], 'exit_short'] = 1
        
        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic, returning the new stoploss value.
        """
        # Use standard stoploss value from strategy parameters
        return self.stoploss

    def custom_entry_price(self, pair: str, current_time: datetime, proposed_rate: float,
                          entry_tag: str, side: str, **kwargs) -> float:
        """
        Custom entry price logic, returning the new entry price.
        """
        # Use proposed rate from exchange
        return proposed_rate

    def custom_exit_price(self, pair: str, trade: 'Trade', current_time: datetime, proposed_rate: float,
                         current_profit: float, exit_tag: str, **kwargs) -> float:
        """
        Custom exit price logic, returning the new exit price.
        """
        # Use proposed rate from exchange
        return proposed_rate

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: str,
                            side: str, **kwargs) -> bool:
        """
        Called before placing a buy order.
        :return: True if the trade should be executed, False otherwise
        """
        # Allow all trades by default
        return True

    def confirm_trade_exit(self, pair: str, trade: 'Trade', order_type: str, amount: float,
                          rate: float, time_in_force: str, exit_reason: str,
                          current_time: datetime, **kwargs) -> bool:
        """
        Called before placing a sell order.
        :return: True if the trade should be executed, False otherwise
        """
        # Allow all trades by default
        return True