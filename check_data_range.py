#!/usr/bin/env python3
"""
<PERSON>ript to check the date range of available data
"""
import pandas as pd
import os
from pathlib import Path

def main():
    """
    Check the date range of available data files
    """
    data_dir = Path("user_data/data/binance")
    
    # Check BTC/USDT 15m data as a reference
    btc_file = data_dir / "BTC_USDT-15m.feather"
    
    if btc_file.exists():
        df = pd.read_feather(btc_file)
        
        # Convert timestamp to datetime
        df['date'] = pd.to_datetime(df['date'], unit='ms')
        
        # Get min and max dates
        min_date = df['date'].min()
        max_date = df['date'].max()
        
        print(f"Data range for BTC/USDT 15m:")
        print(f"Start date: {min_date}")
        print(f"End date: {max_date}")
        print(f"Total candles: {len(df)}")
    else:
        print(f"File {btc_file} not found")

if __name__ == "__main__":
    main()