#!/usr/bin/env python3

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random

# Ensure data directory exists
data_dir = os.path.join('user_data', 'data', 'binance')
os.makedirs(data_dir, exist_ok=True)

# Define parameters
pair = 'BTC_USDT'
timeframe = '5m'
start_date = datetime(2023, 1, 1)
end_date = datetime(2023, 3, 1)
interval = timedelta(minutes=5)

# Generate dates
current_date = start_date
dates = []
while current_date < end_date:
    if current_date.weekday() < 5:  # Only weekdays
        dates.append(current_date)
    current_date += interval

# Generate price data with realistic patterns
def generate_price_series(initial_price, volatility, trend, dates):
    prices = [initial_price]
    for i in range(1, len(dates)):
        # Add some randomness
        random_factor = np.random.normal(0, volatility)
        # Add trend
        trend_factor = trend * (i / len(dates))
        # Calculate new price
        new_price = prices[-1] * (1 + random_factor + trend_factor)
        prices.append(max(100, new_price))  # Ensure price doesn't go below 100
    return prices

# Generate mock data
initial_price = 18000.0
volatility = 0.005
trend = 0.1  # Upward trend

# Generate OHLCV data
df = pd.DataFrame()
df['date'] = dates
prices = generate_price_series(initial_price, volatility, trend, dates)

# Create realistic OHLCV data from the price series
df['open'] = prices
df['high'] = [p * (1 + np.random.uniform(0, 0.01)) for p in prices]
df['low'] = [p * (1 - np.random.uniform(0, 0.01)) for p in prices]
df['close'] = [o * (1 + np.random.normal(0, 0.002)) for o in df['open']]
df['volume'] = [random.uniform(50, 500) for _ in range(len(dates))]

# Format dates to match Freqtrade format
df['date'] = df['date'].map(lambda x: int(x.timestamp() * 1000))

# Save the data
filename = f"{pair}-{timeframe}.json"
filepath = os.path.join(data_dir, filename)

# Convert to expected format
result = df.rename(columns={'date': 'timestamp'})
result = result[['timestamp', 'open', 'high', 'low', 'close', 'volume']]

# Save as JSON in the expected format
result.to_json(filepath, orient='records')
print(f"Created mock data at {filepath}")

# Create another pair with a slight variation
pair2 = 'ETH_USDT'
initial_price2 = 1200.0
prices2 = generate_price_series(initial_price2, volatility * 1.2, trend * 0.8, dates)

df2 = pd.DataFrame()
df2['date'] = dates
df2['open'] = prices2
df2['high'] = [p * (1 + np.random.uniform(0, 0.012)) for p in prices2]
df2['low'] = [p * (1 - np.random.uniform(0, 0.012)) for p in prices2]
df2['close'] = [o * (1 + np.random.normal(0, 0.003)) for o in df2['open']]
df2['volume'] = [random.uniform(100, 1000) for _ in range(len(dates))]

# Format dates
df2['date'] = df2['date'].map(lambda x: int(x.timestamp() * 1000))

# Save the data
filename2 = f"{pair2}-{timeframe}.json"
filepath2 = os.path.join(data_dir, filename2)

# Convert to expected format
result2 = df2.rename(columns={'date': 'timestamp'})
result2 = result2[['timestamp', 'open', 'high', 'low', 'close', 'volume']]

# Save as JSON
result2.to_json(filepath2, orient='records')
print(f"Created mock data at {filepath2}")

print("Mock data generation complete.") 