```
usage: freqtrade show-config [-h] [--userdir PATH] [-c PATH]
                             [--show-sensitive]

options:
  -h, --help            show this help message and exit
  --userdir PATH, --user-data-dir PATH
                        Path to userdata directory.
  -c PATH, --config <PERSON>TH
                        Specify configuration file (default:
                        `userdir/config.json` or `config.json` whichever
                        exists). Multiple --config options may be used. Can be
                        set to `-` to read config from stdin.
  --show-sensitive      Show secrets in the output.

```
