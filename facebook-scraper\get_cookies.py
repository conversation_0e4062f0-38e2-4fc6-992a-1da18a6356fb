import browser_cookie3
import json

def get_facebook_cookies():
    try:
        # Get cookies from your browser
        cookies = browser_cookie3.load(domain_name='facebook.com')
        
        # Convert cookies to dictionary format
        cookie_dict = {cookie.name: cookie.value for cookie in cookies}
        
        # Save cookies to file
        with open('cookies.txt', 'w') as f:
            json.dump(cookie_dict, f)
            
        print("Cookies successfully saved to cookies.txt")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    get_facebook_cookies()
