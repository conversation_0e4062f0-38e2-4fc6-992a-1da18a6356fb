[tool.poetry]
name = "facebook-scraper"
version = "0.2.59"
description = "Scrape Facebook public pages without an API key"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
repository = "https://github.com/kevinzg/facebook-scraper"

[tool.poetry.dependencies]
python = "^3.6"
requests-html = "^0.10.0"
youtube_dl = {version = "*", optional=true}
browser-cookie3 = {version = "*", optional=true}
dateparser = "^1.0.0"
demjson3 = "^3.0.5"

[tool.poetry.dev-dependencies]
ipdb = {version = "*", python = "^3.7"}
ipython = {version = "*", python = "^3.7"}
pytest = "^6.2.2"
pytest-vcr = "^1.0.2"

[tool.poetry.extras]
youtube-dl = ["youtube_dl"]
browser-cookie3 = ["browser-cookie3"]

[tool.poetry.scripts]
facebook-scraper = 'facebook_scraper.__main__:run'

[tool.black]
line-length = 98
target-version = ["py36"]
skip-string-normalization = true

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
filterwarnings = [
    "ignore::UserWarning",
]