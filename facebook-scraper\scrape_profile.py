from facebook_scraper import get_profile, get_posts, set_user_agent
import json
from datetime import datetime
import time

# Profile URL (usiugifulR URL))
profile_url = "https://www.facebook.com/lakshminarayananAIADMK"

def save_to_json(data, filename):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=4, default=str)

def load_cookies():
    try:
        with open("cookies.txt", 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading cookies: {e}")
        return None

try:
    # Set a custom user agent
    set_user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36")
    
    # Load cookies
    cookies = load_cookies()
    if not cookies:
        print("Failed to load cookies")
        exit(1)

    # Get profile data with retry
    print("Fetching profile data...")
    max_retries = 3
    profile_data = None
    
    for attempt in range(max_retries):
        try:
            profile_data = get_profile(profile_url, cookies=cookies)
            break
        except Exception as e:
            print(f"Attempt {attempt + 1}/{max_retries} failed: {e}")
            if attempt < max_retries - 1:
                print("Waiting before retry...")
                time.sleep(5)
            else:
                raise

    if profile_data:
        save_to_json(profile_data, 'profile_data.json')
        print("\nProfile Data:")
        for key, value in profile_data.items():
            print(f"{key}: {value}")

        # Get posts with more options
        print("\nFetching posts...")
        posts_data = []
        try:
            for post in get_posts(
                profile_url,
                pages=5,
                cookies=cookies,
                options={
                    "posts_per_page": 10,
                    "allow_extra_requests": True,
                    "reactions": True,
                    "comments": True
                }
            ):
                print(f"Found post dated: {post.get('time')}")
                posts_data.append(post)
                time.sleep(2)  # Add delay between posts
        
            # Save posts data
            if posts_data:
                save_to_json(posts_data, 'posts_data.json')
                print(f"\nCollected {len(posts_data)} posts and saved to posts_data.json")
            else:
                print("No posts were found")
        
        except Exception as e:
            print(f"Error while fetching posts: {e}")

except Exception as e:
    print(f"Fatal error occurred: {e}")
