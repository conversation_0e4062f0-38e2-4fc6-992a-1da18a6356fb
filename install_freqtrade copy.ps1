# FreqTrade Installation Script
Write-Host "Starting FreqTrade installation..." -ForegroundColor Cyan

# Check if Python is installed
Write-Host "Checking for Python installation..." -ForegroundColor Green
if (Get-Command python -ErrorAction SilentlyContinue) {
    Write-Host "Python is installed" -ForegroundColor Green
} else {
    Write-Host "Python is not installed. Please install Python 3.8 or newer and try again." -ForegroundColor Red
    exit
}

# Check if pip is installed
Write-Host "Checking for pip installation..." -ForegroundColor Green
if (Get-Command pip -ErrorAction SilentlyContinue) {
    Write-Host "pip is installed" -ForegroundColor Green
} else {
    Write-Host "pip is not installed. Please make sure pip is installed with Python." -ForegroundColor Red
    exit
}

# Set path for global installation
$venvPath = ""  # Empty since we're not using venv
$scriptsPath = [System.IO.Path]::Combine([System.IO.Path]::GetDirectoryName((Get-Command python).Source), "Scripts")
Write-Host "Using Python Scripts directory: $scriptsPath" -ForegroundColor Green

# Verify Python and pip
python --version
pip --version

# Install TA-Lib (required for technical analysis)
Write-Host "Installing TA-Lib..." -ForegroundColor Green
pip install --find-links=build_helpers\ --prefer-binary TA-Lib

# Install requirements
Write-Host "Installing requirements..." -ForegroundColor Green
pip install -r requirements.txt

# Install optional dependencies
Write-Host "Would you like to install optional dependencies? (y/n)" -ForegroundColor Yellow
$optionalDeps = Read-Host

if ($optionalDeps -eq "y" -or $optionalDeps -eq "Y") {
    Write-Host "Installing optional dependencies..." -ForegroundColor Green
    pip install -r requirements-hyperopt.txt
    pip install -r requirements-plot.txt
}

# Install FreqTrade in development mode
Write-Host "Installing FreqTrade in development mode..." -ForegroundColor Green
pip install -e .

# Add FreqTrade to PATH
$scriptsPath = "$((Get-Location).Path)\$venvPath\Scripts"
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")

if ($currentPath -notlike "*$scriptsPath*") {
    [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$scriptsPath", "User")
    Write-Host "Added FreqTrade Scripts directory to PATH permanently (user level)." -ForegroundColor Green
    Write-Host "Please restart your terminal for the PATH changes to take effect permanently." -ForegroundColor Yellow
    
    # Update path for current session
    $env:PATH += ";$scriptsPath"
} else {
    Write-Host "FreqTrade Scripts directory is already in your PATH." -ForegroundColor Yellow
}

# Test if FreqTrade is accessible
try {
    freqtrade --version
    Write-Host "FreqTrade installation successful!" -ForegroundColor Green
    Write-Host "You can now use FreqTrade directly from the terminal." -ForegroundColor Cyan
    Write-Host "Example commands:" -ForegroundColor Cyan
    Write-Host "  freqtrade --help" -ForegroundColor White
    Write-Host "  freqtrade trade -c config.json" -ForegroundColor White
} catch {
    Write-Host "FreqTrade installation may not be complete. Please check the error messages." -ForegroundColor Red
}

# Deactivate the virtual environment
deactivate 