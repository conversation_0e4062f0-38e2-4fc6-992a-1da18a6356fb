# Simple FreqTrade Installation Script
Write-Host "Starting FreqTrade installation..." -ForegroundColor Cyan

# Create and activate a virtual environment
$venvPath = ".venv"
if (-not (Test-Path $venvPath)) {
    Write-Host "Creating virtual environment at $venvPath..." -ForegroundColor Green
    python -m venv $venvPath
}

Write-Host "Activating virtual environment..." -ForegroundColor Green
& "$venvPath\Scripts\Activate.ps1"

# Install TA-Lib
Write-Host "Installing TA-Lib..." -ForegroundColor Green
pip install --find-links=build_helpers\ --prefer-binary TA-Lib

# Install core requirements
Write-Host "Installing core requirements..." -ForegroundColor Green
pip install -r requirements.txt

# Install FreqTrade in development mode
Write-Host "Installing FreqTrade in development mode..." -ForegroundColor Green
pip install -e .

# Update PATH for current session
$scriptsPath = "$((Get-Location).Path)\$venvPath\Scripts"
$env:PATH += ";$scriptsPath"

Write-Host "FreqTrade is now installed and available in this terminal session." -ForegroundColor Green
Write-Host "Try running 'freqtrade --version' to verify." -ForegroundColor Yellow 