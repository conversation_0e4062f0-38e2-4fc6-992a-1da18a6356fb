#!/usr/bin/env python3
"""
Script to make the shell script executable
"""
import os
import stat

def main():
    """
    Make the shell script executable
    """
    script_path = "run_backtest.sh"
    
    # Get current permissions
    current_permissions = os.stat(script_path).st_mode
    
    # Add execute permission
    new_permissions = current_permissions | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH
    
    # Set new permissions
    os.chmod(script_path, new_permissions)
    
    print(f"Made {script_path} executable")

if __name__ == "__main__":
    main()