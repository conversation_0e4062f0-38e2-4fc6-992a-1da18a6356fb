"""
Offcloud API Client
Handles all interactions with the Offcloud API
"""

import requests
import time
from typing import Dict, Any, List, Optional, Union
import logging

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class OffcloudClient:
    """Client for interacting with Offcloud API"""
    
    BASE_URL = 'https://offcloud.com/api/'
    
    def __init__(self, api_key: str, max_retries: int = 3, retry_delay: float = 1.0):
        """
        Initialize the Offcloud API client
        
        Args:
            api_key: Offcloud API key
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
        """
        self.api_key = api_key
        self.session = requests.Session()
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum time between requests in seconds
        
    def _rate_limit(self) -> None:
        """Implement rate limiting to prevent API abuse"""
        now = time.time()
        time_since_last_request = now - self.last_request_time
        if time_since_last_request < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last_request)
        self.last_request_time = time.time()

    def _request(self, endpoint: str, data: Optional[Dict[str, Any]] = None, method: str = 'POST') -> Dict[str, Any]:
        """
        Make a request to the Offcloud API
        
        Args:
            endpoint: API endpoint to call
            data: Request data
            method: HTTP method (GET or POST)
            
        Returns:
            API response as dictionary
        """
        url = f"{self.BASE_URL}{endpoint}?key={self.api_key}"
        retries = 0
        
        while retries <= self.max_retries:
            try:
                self._rate_limit()
                
                if method.upper() == 'GET':
                    response = self.session.get(url, timeout=30)
                else:
                    response = self.session.post(url, json=data, timeout=30)
                
                response.raise_for_status()
                return response.json()

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # Too Many Requests
                    retry_after = int(e.response.headers.get('Retry-After', self.retry_delay))
                    logger.warning(f"Rate limited. Retrying after {retry_after} seconds.")
                    time.sleep(retry_after)
                    retries += 1
                    continue
                logger.error(f"HTTP Error: {str(e)}")
                return {'error': f'HTTP Error: {str(e)}', 'status_code': e.response.status_code}

            except requests.exceptions.Timeout:
                logger.warning(f"Request timed out. Retry {retries+1}/{self.max_retries}")
                if retries < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** retries))  # Exponential backoff
                    retries += 1
                    continue
                return {'error': 'Request timed out after multiple retries'}

            except requests.exceptions.RequestException as e:
                logger.error(f"Request Failed: {str(e)}")
                if retries < self.max_retries:
                    time.sleep(self.retry_delay)
                    retries += 1
                    continue
                return {'error': f'Request Failed: {str(e)}'}

            except ValueError as e:  # JSON decode error
                logger.error(f"Invalid response format: {str(e)}")
                return {'error': f'Invalid response format: {str(e)}'}

        return {'error': 'Max retries exceeded'}

    def get_account_info(self) -> Dict[str, Any]:
        """
        Get account information
        
        Returns:
            Account information including premium status, days left, etc.
        """
        return self._request('account/info', method='GET')
    
    def instant_download(self, url: str, proxy_id: Optional[str] = None, remote_option: Optional[str] = None) -> Dict[str, Any]:
        """
        Create an instant download
        
        Args:
            url: URL to download
            proxy_id: Optional proxy ID to use
            remote_option: Optional remote option (clouddownload, torrent, youtube)
            
        Returns:
            Download information including requestId
        """
        data = {'url': url}
        if proxy_id:
            data['proxyId'] = proxy_id
        if remote_option:
            data['remoteOption'] = remote_option
        return self._request('instant', data)
    
    def cloud_download(self, url: str) -> Dict[str, Any]:
        """
        Create a cloud download
        
        Args:
            url: URL to download
            
        Returns:
            Download information including requestId
        """
        return self._request('cloud', {'url': url})
    
    def remote_download(self, url: str, remote_option_id: str, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a remote download
        
        Args:
            url: URL to download
            remote_option_id: Remote option ID
            folder_id: Optional folder ID for cloud storage
            
        Returns:
            Download information including requestId
        """
        data = {
            'url': url,
            'remoteOptionId': remote_option_id
        }
        if folder_id:
            data['folderId'] = folder_id
        return self._request('remote', data)
    
    def check_cloud_status(self, request_id: str) -> Dict[str, Any]:
        """
        Check status of a cloud download
        
        Args:
            request_id: Request ID to check
            
        Returns:
            Status information
        """
        return self._request('cloud/status', {'requestId': request_id})
    
    def check_remote_status(self, request_id: str) -> Dict[str, Any]:
        """
        Check status of a remote download
        
        Args:
            request_id: Request ID to check
            
        Returns:
            Status information
        """
        return self._request('remote/status', {'requestId': request_id})
    
    def check_instant_status(self, request_id: str) -> Dict[str, Any]:
        """
        Check status of an instant download
        
        Args:
            request_id: Request ID to check
            
        Returns:
            Status information
        """
        return self._request('instant/status', {'requestId': request_id})
    
    def list_remote_accounts(self) -> Dict[str, Any]:
        """
        List remote accounts
        
        Returns:
            List of remote accounts
        """
        return self._request('remote/accounts', method='GET')

    def get_supported_services(self) -> Dict[str, Any]:
        """
        Get supported services
        
        Returns:
            List of supported services
        """
        return self._request('supported-services', method='GET')

    def get_quota(self) -> Dict[str, Any]:
        """
        Get account quota
        
        Returns:
            Quota information
        """
        return self._request('quota', method='GET')

    def get_activity(self, limit: int = 10) -> Dict[str, Any]:
        """
        Get account activity
        
        Args:
            limit: Maximum number of activities to return
            
        Returns:
            Activity information
        """
        return self._request('activity', {'limit': limit})

    def cancel_download(self, request_id: str) -> Dict[str, Any]:
        """
        Cancel a download
        
        Args:
            request_id: Request ID to cancel
            
        Returns:
            Cancellation status
        """
        return self._request('cancel', {'requestId': request_id})
        
    def explore_torrent(self, url: str) -> Dict[str, Any]:
        """
        Explore contents of a torrent file
        
        Args:
            url: Torrent URL
            
        Returns:
            Torrent contents
        """
        return self._request('torrent/explore', {'url': url})

    def get_proxy_list(self) -> Dict[str, Any]:
        """
        Get list of available proxies
        
        Returns:
            List of proxies
        """
        return self._request('proxy/list', method='GET')

    def get_download_history(self) -> Dict[str, Any]:
        """
        Get download history
        
        Returns:
            Download history
        """
        return self._request('download/history', method='GET')
    
    def get_download_status(self, request_id: str) -> Dict[str, Any]:
        """
        Get status of any download type
        
        Args:
            request_id: Request ID to check
            
        Returns:
            Status information
        """
        # Try all status endpoints until one works
        cloud_status = self.check_cloud_status(request_id)
        if 'error' not in cloud_status:
            return cloud_status
            
        remote_status = self.check_remote_status(request_id)
        if 'error' not in remote_status:
            return remote_status
            
        instant_status = self.check_instant_status(request_id)
        if 'error' not in instant_status:
            return instant_status
            
        return {'error': 'Could not find download with this request ID'}

    def detect_url_type(self, url: str) -> str:
        """
        Detect URL type and return appropriate remote option
        
        Args:
            url: URL to detect
            
        Returns:
            Remote option type (torrent, youtube, clouddownload)
        """
        url_lower = url.lower()
        if any(x in url_lower for x in ['.torrent', 'magnet:', 'btih']):
            return 'torrent'
        elif any(x in url_lower for x in ['youtube.com', 'youtu.be']):
            return 'youtube'
        return 'clouddownload'