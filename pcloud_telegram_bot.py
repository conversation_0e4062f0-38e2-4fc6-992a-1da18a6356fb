import os
from dotenv import load_dotenv
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes
from pcloud import PyCloud

# Load environment variables
load_dotenv()

# Configure your credentials
TELEGRAM_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
PCLOUD_EMAIL = os.getenv('PCLOUD_EMAIL')
PCLOUD_PASSWORD = os.getenv('PCLOUD_PASSWORD')

# Initialize pCloud client
pc = PyCloud(PCLOUD_EMAIL, PCLOUD_PASSWORD)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    await update.message.reply_text(
        'Welcome to pCloud Bot! Use /list to see your files and folders.'
    )

async def list_files(update: Update, context: ContextTypes.DEFAULT_TYPE, folder_id=0):
    try:
        # Get folder contents
        contents = pc.listfolder(folder_id)
        
        # Format the response
        response = "📁 Files and Folders:\n\n"
        
        # Add folders
        for item in contents['contents']:
            if item['isfolder']:
                response += f"📁 {item['name']}\n"
            else:
                response += f"📄 {item['name']}\n"
        
        await update.message.reply_text(response)
    except Exception as e:
        await update.message.reply_text(f"Error: {str(e)}")

async def get_link(update: Update, context: ContextTypes.DEFAULT_TYPE):
    try:
        # Check if file name is provided
        if not context.args:
            await update.message.reply_text("Please provide a file name!")
            return

        file_name = ' '.join(context.args)
        
        # Search for the file
        files = pc.listfolder(recursive=True)
        file_id = None
        
        for item in files['contents']:
            if not item['isfolder'] and item['name'] == file_name:
                file_id = item['fileid']
                break
        
        if file_id:
            # Generate download link
            link = pc.getfilelink(file_id)
            await update.message.reply_text(f"Download link: {link}")
        else:
            await update.message.reply_text("File not found!")
            
    except Exception as e:
        await update.message.reply_text(f"Error: {str(e)}")

def main():
    # Create application
    application = Application.builder().token(TELEGRAM_TOKEN).build()

    # Add command handlers
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("list", list_files))
    application.add_handler(CommandHandler("getlink", get_link))

    # Start the bot
    print("Starting bot...")
    application.run_polling()

if __name__ == "__main__":
    main()