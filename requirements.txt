numpy==1.26.4
pandas==2.2.3
ta==0.11.0
bottleneck==1.4.2
numexpr==2.10.2
pandas-ta==0.3.14b

ccxt==4.4.68
cryptography==44.0.2
aiohttp==3.9.5
SQLAlchemy==2.0.39
python-telegram-bot==22.0
# can't be hard-pinned due to telegram-bot pinning httpx with ~
httpx>=0.24.1
humanize==4.12.1
cachetools==5.5.2
requests==2.32.3
urllib3==2.3.0
jsonschema==4.23.0
technical==1.5.0
tabulate==0.9.0
pycoingecko==3.2.0
jinja2==3.1.6
joblib==1.4.2
rich==13.9.4
pyarrow==19.0.1; platform_machine != 'armv7l'

# find first, C search in arrays
py_find_1st==1.1.7

# Load ticker files 30% faster
python-rapidjson==1.20
# Properly format api responses
orjson==3.10.15

# Notify systemd
sdnotify==0.3.2

# API Server
fastapi==0.115.11
pydantic==2.10.6
uvicorn==0.34.0
pyjwt==2.10.1
aiofiles==24.1.0
psutil==7.0.0

# Building config files interactively
questionary==2.1.0
prompt-toolkit==3.0.50
# Extensions to datetime library
python-dateutil==2.9.0.post0
pytz==2025.1

#Futures
schedule==1.2.2

#WS Messages
websockets==15.0.1
janus==2.0.0

ast-comments==1.2.2
packaging==24.2
