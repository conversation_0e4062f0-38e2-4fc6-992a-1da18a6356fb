#!/usr/bin/env python3
"""
Script to run backtesting for StrendStrategy
"""
import sys
import json
import asyncio
from pathlib import Path
from freqtrade.configuration import Configuration
from freqtrade.commands import start_backtesting
from freqtrade.optimize.backtesting import Backtesting
from freqtrade.resolvers import StrategyResolver
from freqtrade.commands.optimize_commands import start_backtesting
from freqtrade.commands.arguments import Arguments

# Set up SelectorEventLoop for Windows
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add the parent directory to sys.path
sys.path.append(str(Path(__file__).parent))

# Define configuration
config = {
    "max_open_trades": 5,
    "stake_currency": "USDT",
    "stake_amount": "unlimited",
    "tradable_balance_ratio": 0.99,
    "fiat_display_currency": "USD",
    "timeframe": "15m",
    "dry_run": True,
    "cancel_open_orders_on_exit": False,
    "datadir": "./user_data/data",
    "user_data_dir": "./user_data",
    "strategy": "StrendStrategy",
    "strategy_path": "./user_data/strategies",
    "backtest_breakdown": ["day", "week"],
    "export": "trades",
    "exportfilename": "backtest-result.json"
}

# Define pairs to backtest
pairs = [
    "BTC/USDT", 
    "ETH/USDT", 
    "BNB/USDT", 
    "ADA/USDT", 
    "SOL/USDT",
    "XRP/USDT",
    "DOT/USDT",
    "DOGE/USDT",
    "AVAX/USDT",
    "LINK/USDT"  # Replaced MATIC with LINK since we have data for it
]

# Define timerange (adjust as needed)
timerange = "20210101-20220101"  # Year 2021

def main():
    """
    Main function to run backtesting
    """
    print("Starting backtesting for StrendStrategy...")
    
    # Create configuration
    configuration = Configuration(config)
    config_dict = configuration.get_config()
    
    # Set pairs and timerange
    config_dict["pairs"] = pairs
    config_dict["timerange"] = timerange
    
    # Initialize backtesting object
    backtesting = Backtesting(config_dict)
    
    # Load strategy
    strategy = StrategyResolver.load_strategy(config_dict)
    
    # Start backtesting
    backtesting.start()
    
    # Print results
    print("\nBacktesting completed!")
    print(f"Results saved to {config['exportfilename']}")
    
    # Load and display summary of results
    try:
        with open(config['exportfilename'], 'r') as f:
            results = json.load(f)
            
        print("\nSummary:")
        print(f"Total trades: {len(results.get('trades', []))}")
        
        stats = results.get('strategy', {}).get('results_per_pair', {})
        for pair, pair_stats in stats.items():
            profit = pair_stats.get('profit_abs', 0)
            trades = pair_stats.get('trades', 0)
            print(f"{pair}: {profit:.2f} USDT ({trades} trades)")
            
        total_profit = results.get('strategy', {}).get('profit_total_abs', 0)
        win_rate = results.get('strategy', {}).get('win_ratio', 0) * 100
        
        print(f"\nTotal profit: {total_profit:.2f} USDT")
        print(f"Win rate: {win_rate:.2f}%")
        
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Could not load results file: {e}")

if __name__ == "__main__":
    # Create the arguments
    args = [
        'backtesting',
        '--config', 'user_data/config.json',
        '--strategy', 'EnhancedStrendStrategy', 
        '--timerange', '20230101-20230301',
        '--timeframe', '5m',
        '--datadir', 'user_data/data/binance'
    ]
    
    # Parse the arguments
    args = Arguments(args).get_parsed_arg()
    
    # Start backtesting
    start_backtesting(args)