#!/usr/bin/env python3
"""
Script to run backtesting for StrendStrategy using the Freqtrade API
"""
import logging
from pathlib import Path
import json

from freqtrade.configuration import Configuration
from freqtrade.data.history import load_pair_history
from freqtrade.optimize.backtesting import Backtesting
from freqtrade.resolvers import StrategyResolver
from freqtrade.data.dataprovider import DataProvider
from freqtrade.plugins.pairlistmanager import PairListManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

def main():
    """
    Main function to run backtesting
    """
    logger.info("Starting backtesting for StrendStrategy...")
    
    # Load config
    config_file = Path("user_data/config.json")
    
    if not config_file.exists():
        logger.error(f"Config file {config_file} not found!")
        return
    
    # Load configuration
    config = Configuration.from_files([str(config_file)])
    config_dict = config.get_config()
    
    # Set pairs and timerange
    config_dict["pairs"] = [
        "BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT",
        "XRP/USDT", "DOT/USDT", "DOGE/USDT", "AVAX/USDT", "LINK/USDT"
    ]
    config_dict["timerange"] = "20210101-20220101"
    
    # Initialize backtesting object
    backtesting = Backtesting(config_dict)
    
    # Load strategy
    strategy = StrategyResolver.load_strategy(config_dict)
    
    # Load data
    data = backtesting.load_bt_data()
    
    # Run backtesting
    min_date, max_date = backtesting.backtest_one_strategy(strategy, data, "StrendStrategy")
    
    # Generate and print stats
    results = backtesting.generate_backtest_stats(data, strategy.get_strategy_name())
    
    # Print results
    print("\nBacktesting Results:")
    print(f"Timerange: {min_date} to {max_date}")
    print(f"Total trades: {results['total_trades']}")
    print(f"Win rate: {results['win_ratio'] * 100:.2f}%")
    print(f"Profit: {results['profit_total_abs']:.2f} {config_dict['stake_currency']}")
    print(f"Profit %: {results['profit_total'] * 100:.2f}%")
    print(f"Max Drawdown: {results['max_drawdown_abs']:.2f} {config_dict['stake_currency']} ({results['max_drawdown']:.2f}%)")
    
    # Save results to file
    results_file = Path("backtest-results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=4)
    
    print(f"\nDetailed results saved to {results_file}")

if __name__ == "__main__":
    main()