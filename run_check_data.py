#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the data range check
"""
import subprocess
import sys

def main():
    """
    Run the data range check script
    """
    try:
        result = subprocess.run(
            [sys.executable, "check_data_range.py"],
            check=True,
            capture_output=True,
            text=True
        )
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error running script: {e}")
        print(f"Error output: {e.stderr}")

if __name__ == "__main__":
    main()