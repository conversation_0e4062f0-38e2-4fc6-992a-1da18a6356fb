#!/usr/bin/env python
"""
Simple wrapper for freqtrade to make it easier to run without having to type
python -m freqtrade.main each time
"""
import sys
import runpy

if __name__ == "__main__":
    # Store original arguments
    original_args = list(sys.argv)
    
    # Check if we're just asking for version
    if len(sys.argv) == 2 and sys.argv[1] == "--version":
        # For --version, we need to keep sys.argv[0]
        # but replace it with the module name
        sys.argv = ["freqtrade"]
    else:
        # Otherwise, remove first argument (script name) and pass the rest
        sys.argv = sys.argv[1:]
    
    # Run freqtrade.main with the modified arguments
    runpy.run_module("freqtrade.main", run_name="__main__") 