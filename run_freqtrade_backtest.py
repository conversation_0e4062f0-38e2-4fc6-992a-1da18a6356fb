#!/usr/bin/env python3
"""
<PERSON>ript to run Freqtrade backtesting using the CLI
"""
import subprocess
import sys
import os
from pathlib import Path

def main():
    """
    Run Freqtrade backtesting
    """
    # Ensure we're in the correct directory
    script_dir = Path(__file__).parent.absolute()
    os.chdir(script_dir)
    
    # Define the command
    cmd = [
        "freqtrade",
        "backtesting",
        "--config", "user_data/config.json",
        "--strategy", "StrendStrategy",
        "--timerange", "20210101-20220101",
        "--timeframe", "15m",
        "--export", "trades",
        "--pairs", "BTC/USDT ETH/USDT BNB/USDT ADA/USDT SOL/USDT XRP/USDT DOT/USDT DOGE/USDT AVAX/USDT LINK/USDT"
    ]
    
    try:
        # Run the command
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(
            cmd,
            check=True,
            text=True
        )
        print("Backtesting completed successfully!")
        
        # Check if results file exists
        results_file = Path("user_data/backtest_results/backtest-result.json")
        if results_file.exists():
            print(f"Results saved to {results_file}")
        else:
            print("Results file not found. Check the logs for details.")
            
    except subprocess.CalledProcessError as e:
        print(f"Error running Freqtrade: {e}")
        
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()