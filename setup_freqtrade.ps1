# Add freqtrade Scripts directory to the PATH
$scriptsPath = "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\Scripts"

# Check if the Scripts directory exists
if (Test-Path $scriptsPath) {
    # Add to PATH for current session
    $env:PATH += ";$scriptsPath"
    Write-Host "Added freqtrade Scripts directory to PATH for current session." -ForegroundColor Green
    
    # Add to PATH permanently (user level)
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
    if ($currentPath -notlike "*$scriptsPath*") {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$scriptsPath", "User")
        Write-Host "Added freqtrade Scripts directory to PATH permanently (user level)." -ForegroundColor Green
        Write-Host "Please restart your terminal for the PATH changes to take effect permanently." -ForegroundColor Yellow
    } else {
        Write-Host "freqtrade Scripts directory is already in your PATH." -ForegroundColor Yellow
    }
} else {
    Write-Host "Error: Scripts directory not found at $scriptsPath" -ForegroundColor Red
}

# Test if freqtrade is accessible
try {
    $freqtradeVersion = freqtrade --version
    Write-Host "freqtrade command is working:" -ForegroundColor Green
    Write-Host $freqtradeVersion
} catch {
    Write-Host "Error: freqtrade command not found. Please check your installation." -ForegroundColor Red
} 