# Dummy talib module to satisfy imports
import ta
import numpy as np
import pandas as pd
from ta import trend, momentum, volatility

# Define common TA-Lib functions using ta package equivalents
def SMA(data, timeperiod=14):
    """Simple Moving Average"""
    if isinstance(data, pd.Series):
        return trend.sma_indicator(data, window=timeperiod).values
    if isinstance(data, pd.DataFrame):
        # Assume we're operating on close price
        return trend.sma_indicator(data['close'], window=timeperiod).values
    return np.nan_to_num(pd.Series(data).rolling(window=timeperiod).mean().values)

def EMA(data, timeperiod=14):
    """Exponential Moving Average"""
    if isinstance(data, pd.Series):
        return trend.ema_indicator(data, window=timeperiod).values
    if isinstance(data, pd.DataFrame):
        # Assume we're operating on close price
        return trend.ema_indicator(data['close'], window=timeperiod).values
    return np.nan_to_num(pd.Series(data).ewm(span=timeperiod, adjust=False).mean().values)

def WMA(data, timeperiod=14):
    """Weighted Moving Average"""
    weights = np.arange(1, timeperiod + 1)
    if isinstance(data, pd.Series):
        result = data.rolling(window=timeperiod).apply(lambda x: np.sum(weights * x) / weights.sum(), raw=True)
        return np.nan_to_num(result.values)
    if isinstance(data, pd.DataFrame):
        # Assume we're operating on close price
        result = data['close'].rolling(window=timeperiod).apply(lambda x: np.sum(weights * x) / weights.sum(), raw=True)
        return np.nan_to_num(result.values)
    return np.nan_to_num(pd.Series(data).rolling(window=timeperiod).apply(lambda x: np.sum(weights * x) / weights.sum(), raw=True).values)

def RSI(data, timeperiod=14):
    """Relative Strength Index"""
    if isinstance(data, pd.Series):
        return momentum.rsi(data, window=timeperiod).values
    if isinstance(data, pd.DataFrame):
        # Assume we're operating on close price
        return momentum.rsi(data['close'], window=timeperiod).values
    return np.nan_to_num(momentum.rsi(pd.Series(data), window=timeperiod).values)

def MACD(data, fastperiod=12, slowperiod=26, signalperiod=9):
    """Moving Average Convergence/Divergence"""
    if isinstance(data, pd.DataFrame):
        data = data['close']
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    macd_line = trend.ema_indicator(data, window=fastperiod) - trend.ema_indicator(data, window=slowperiod)
    signal_line = trend.ema_indicator(macd_line, window=signalperiod)
    histogram = macd_line - signal_line
    
    return np.nan_to_num(macd_line.values), np.nan_to_num(signal_line.values), np.nan_to_num(histogram.values)

def BBANDS(data, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0):
    """Bollinger Bands"""
    if isinstance(data, pd.DataFrame):
        data = data['close']
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    upper = volatility.bollinger_hband(data, window=timeperiod, window_dev=nbdevup)
    middle = volatility.bollinger_mavg(data, window=timeperiod)
    lower = volatility.bollinger_lband(data, window=timeperiod, window_dev=nbdevdn)
    
    # Return as dictionary instead of tuple for compatibility with strategy
    return {
        'upperband': np.nan_to_num(upper.values),
        'middleband': np.nan_to_num(middle.values),
        'lowerband': np.nan_to_num(lower.values)
    }

# Add more functions as needed
def ATR(data_or_high, low=None, close=None, timeperiod=14):
    """Average True Range"""
    # If first argument is a DataFrame and no other args, extract high/low/close from it
    if isinstance(data_or_high, pd.DataFrame) and low is None and close is None:
        df = data_or_high
        high = df['high']
        low = df['low']
        close = df['close']
    else:
        high = data_or_high
        
    if not isinstance(high, pd.Series):
        high = pd.Series(high)
    if not isinstance(low, pd.Series):
        low = pd.Series(low)
    if not isinstance(close, pd.Series):
        close = pd.Series(close)
    
    tr = np.maximum(
        high - low,
        np.maximum(
            np.abs(high - close.shift(1)),
            np.abs(low - close.shift(1))
        )
    )
    atr = tr.rolling(window=timeperiod).mean()
    return np.nan_to_num(atr.values)

def STDDEV(data, timeperiod=5, nbdev=1):
    """Standard Deviation over a period"""
    if isinstance(data, pd.DataFrame):
        data = data['close']
    if not isinstance(data, pd.Series):
        data = pd.Series(data)
    
    stddev = data.rolling(window=timeperiod).std() * nbdev
    return np.nan_to_num(stddev.values)

def SAR(high, low, acceleration=0.02, maximum=0.2):
    """Parabolic SAR"""
    if not isinstance(high, pd.Series):
        high = pd.Series(high)
    if not isinstance(low, pd.Series):
        low = pd.Series(low)
    
    # A simplified implementation
    sar = volatility.psar_up(high, low, close=None)
    return np.nan_to_num(sar.values)

# Candlestick pattern functions - simplified implementation
def CDLHAMMER(open, high, low, close):
    """Hammer candlestick pattern"""
    if isinstance(open, pd.DataFrame):
        df = open
        return np.zeros(len(df))
    return np.zeros(len(close))

def CDLENGULFING(open, high, low, close):
    """Engulfing candlestick pattern"""
    if isinstance(open, pd.DataFrame):
        df = open
        return np.zeros(len(df))
    return np.zeros(len(close))

def CDLDOJI(open, high, low, close):
    """Doji candlestick pattern"""
    if isinstance(open, pd.DataFrame):
        df = open
        return np.zeros(len(df))
    return np.zeros(len(close))

# Provide constants for compatibility
MA_Type = type('MA_Type', (), {'SMA': 0, 'EMA': 1, 'WMA': 2, 'DEMA': 3, 'TEMA': 4, 'TRIMA': 5, 'KAMA': 6, 'MAMA': 7, 'T3': 8})() 