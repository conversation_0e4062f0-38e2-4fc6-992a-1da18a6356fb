from PIL import Image, ImageDraw, ImageFont
import matplotlib.font_manager as fm
import os

def list_available_fonts():
    """List all available fonts that might support Tamil"""
    fonts = fm.findSystemFonts()
    tamil_fonts = []
    
    # Common Tamil font names to look for
    tamil_font_names = ['latha', 'nirmala', 'tamil', 'vijaya', 'bamini', 'inaimathi', 'kavivanar']
    
    for font in fonts:
        font_name = os.path.basename(font).lower()
        # Check if any Tamil font name is in the font path
        if any(tamil_name in font_name for tamil_name in tamil_font_names):
            tamil_fonts.append(font)
    
    return tamil_fonts

def create_tamil_flag_text(text, font_path=None, font_size=48, output_file="aiadmk_flag_text.png"):
    """
    Create an image with Tamil text in AIADMK flag colors (black, white, red)
    
    Args:
        text: Tamil text to display
        font_path: Path to a Tamil font file
        font_size: Font size
        output_file: Output image filename
    """
    # If no font path provided, try to find a suitable Tamil font
    if not font_path:
        tamil_fonts = list_available_fonts()
        if tamil_fonts:
            font_path = tamil_fonts[0]
            print(f"Using font: {os.path.basename(font_path)}")
        else:
            # Fallback to a default font
            try:
                font_path = fm.findfont(fm.FontProperties(family='DejaVu Sans'))
                print("No Tamil font found. Using DejaVu Sans as fallback.")
            except:
                print("Warning: Could not find a suitable font. Text may not render correctly.")
                font_path = None
    
    # Split text into three parts for black, white, red
    n = len(text)
    part1 = text[:n//3]
    part2 = text[n//3:2*n//3]
    part3 = text[2*n//3:]
    
    # Image size
    img_width = 1600
    img_height = 200
    img = Image.new('RGB', (img_width, img_height), color='white')
    draw = ImageDraw.Draw(img)
    
    try:
        # Load font
        if font_path:
            font = ImageFont.truetype(font_path, font_size)
        else:
            # Use default font if no font path available
            font = ImageFont.load_default()
        
        # Calculate positions
        x = 30
        y = (img_height - font_size) // 2  # Center vertically
        
        # Draw each part in its color
        # Black
        draw.text((x, y), part1, font=font, fill='black')
        
        # Calculate width of first part (for PIL >= 8.0.0)
        try:
            # For newer PIL versions
            bbox1 = draw.textbbox((x, y), part1, font=font)
            w1 = bbox1[2] - bbox1[0]
        except AttributeError:
            # For older PIL versions
            w1, _ = draw.textsize(part1, font=font)
        
        x += w1
        
        # White with black outline for visibility
        draw.text((x, y), part2, font=font, fill='white', stroke_width=2, stroke_fill='black')
        
        # Calculate width of second part
        try:
            bbox2 = draw.textbbox((x, y), part2, font=font)
            w2 = bbox2[2] - bbox2[0]
        except AttributeError:
            w2, _ = draw.textsize(part2, font=font)
        
        x += w2
        
        # Red
        draw.text((x, y), part3, font=font, fill='red')
        
        # Save image
        img.save(output_file)
        print(f"Image saved as {output_file}")
        
        return output_file, font_path
    
    except Exception as e:
        print(f"Error creating image: {e}")
        return None, None

if __name__ == "__main__":
    # Tamil text
    text = "தென் சென்னை வடக்கு கிழக்கு மாவட்டம் - தகவல் தொழில்நுட்ப பிரிவு"
    
    # List available Tamil fonts
    print("Searching for Tamil fonts...")
    tamil_fonts = list_available_fonts()
    
    if tamil_fonts:
        print("\nAvailable Tamil fonts:")
        for i, font in enumerate(tamil_fonts):
            print(f"{i+1}. {os.path.basename(font)}")
        
        # Use the first Tamil font found
        font_path = tamil_fonts[0]
    else:
        print("No Tamil fonts found. Will try to use a default font.")
        font_path = None
    
    # Create the image
    output_file, used_font = create_tamil_flag_text(text, font_path)
    
    if output_file:
        print(f"\nSuccessfully created image with font: {os.path.basename(used_font) if used_font else 'default'}")
        print(f"Image saved as: {output_file}")
    else:
        print("Failed to create image.")
