import sys
import importlib
import os

print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")

try:
    import freqtrade
    print(f"Freqtrade version: {freqtrade.__version__}")
except ImportError as e:
    print(f"Error importing freqtrade: {e}")

try:
    from freqtrade.commands.optimize_commands import start_backtesting
    print("Successfully imported start_backtesting")
except ImportError as e:
    print(f"Error importing start_backtesting: {e}")

# Try to load the strategy
try:
    sys.path.append(os.path.join(os.getcwd(), 'user_data', 'strategies'))
    strat_module = importlib.import_module('EnhancedStrendStrategy')
    print(f"Strategy module imported: {strat_module}")
except Exception as e:
    print(f"Error loading strategy: {e}") 