#!/usr/bin/env python3

import sys
import os
from pathlib import Path

# Add the parent directory and strategies directory to sys.path
sys.path.append(str(Path(__file__).parent))
sys.path.append(os.path.join(os.getcwd(), 'user_data', 'strategies'))

# Import the strategy
from EnhancedStrendStrategy import EnhancedStrendStrategy

def main():
    """Test the strategy implementation directly"""
    # Create an instance of the strategy
    strategy = EnhancedStrendStrategy({})
    
    # Print strategy info
    print(f"Strategy name: {strategy.get_strategy_name()}")
    print(f"Timeframe: {strategy.timeframe}")
    print(f"Minimal ROI: {strategy.minimal_roi}")
    print(f"Stoploss: {strategy.stoploss}")
    
    # Print indicators used
    print("\nIndicators:")
    for indicator in dir(strategy):
        if not indicator.startswith('__') and not callable(getattr(strategy, indicator)):
            try:
                value = getattr(strategy, indicator)
                if isinstance(value, (int, float, str, list, dict, tuple)):
                    print(f"  {indicator}: {value}")
            except:
                pass

if __name__ == "__main__":
    main() 